<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试重复Loading图标修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        p {
            margin: 15px 0;
            padding: 15px;
            background-color: #fff;
            border-left: 4px solid #007bff;
            border-radius: 4px;
            position: relative;
        }
        strong {
            color: #dc3545;
            font-weight: bold;
        }
        em {
            color: #28a745;
            font-style: italic;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        /* 翻译相关样式 */
        .split-paragraph {
            border-left-color: #ffc107 !important;
            background-color: #fff3cd !important;
        }
        .segment-wrapper {
            border: 1px dashed #6c757d;
            margin: 8px 0;
            padding: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            position: relative;
        }
        .translate-icon {
            display: inline-block !important;
            width: 16px !important;
            height: 16px !important;
            background-color: #007bff !important;
            border-radius: 50% !important;
            margin-left: 8px !important;
            position: relative !important;
            animation: pulse 1.5s infinite !important;
        }
        .translation-result {
            background-color: #d4edda !important;
            border: 1px solid #c3e6cb !important;
            padding: 8px !important;
            margin: 8px 0 !important;
            border-radius: 4px !important;
            color: #155724 !important;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        /* 调试面板样式 */
        .debug-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            max-height: 600px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 11px;
            font-family: monospace;
            z-index: 10000;
            overflow-y: auto;
            display: none;
        }
        
        .control-buttons {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 10001;
        }
        
        .control-buttons button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
    </style>
</head>
<body>
    <h1>重复Loading图标修复测试</h1>
    
    <div class="control-buttons">
        <button class="btn-primary" onclick="startTranslation()">开始翻译测试</button>
        <button class="btn-success" onclick="toggleDebug()">显示调试</button>
        <button class="btn-danger" onclick="clearDebug()">清空日志</button>
        <button class="btn-warning" onclick="checkLoadingIcons()">检查Loading图标</button>
    </div>
    
    <div id="debug-panel" class="debug-panel"></div>
    
    <div class="test-section">
        <h2 class="test-title">测试1: 包含br标签的段落（应该拆分，每行一个loading）</h2>
        <p>这是第一行文字，包含<strong>加粗文字</strong>，还有普通文字。<br>这是第二行文字，包含<em>斜体文字</em>，以及更多内容。<br>这是第三行文字，包含<code>代码文字</code>和结尾。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试2: 简单段落（应该只有一个loading图标）</h2>
        <p>这是一个简单的段落，不包含换行标签，应该使用原有的翻译逻辑进行处理，只显示一个loading图标。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试3: 复杂拆分段落（4个分段，应该有4个loading）</h2>
        <p>第一段内容，包含<strong>重要信息</strong>。<br>第二段内容，包含<a href="#">链接文字</a>。<br>第三段内容，包含<span style="color: blue;">彩色文字</span>。<br>第四段内容，这是最后一段。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试4: 嵌套HTML的拆分段落</h2>
        <p>这里有<strong>加粗的<em>斜体</em>文字</strong>，还有普通文字。<br>这里有<code>代码块<strong>加粗代码</strong></code>内容。<br>最后是<mark>高亮文字</mark>结尾。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试5: 只有一个br的段落（2个分段）</h2>
        <p>第一行内容，包含一些文字。<br>第二行内容，这是最后一行。</p>
    </div>

    <script>
        let debugVisible = false;
        let debugPanel = document.getElementById('debug-panel');
        
        // 模拟Chrome扩展环境
        if (!window.chrome) {
            window.chrome = {
                runtime: {
                    onMessage: {
                        addListener: function(callback) {
                            log('模拟Chrome扩展消息监听器已添加');
                        }
                    }
                }
            };
        }

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${message}`;
            logEntry.style.borderBottom = '1px solid #333';
            logEntry.style.padding = '3px 0';
            logEntry.style.wordBreak = 'break-word';
            debugPanel.appendChild(logEntry);
            if (debugVisible) {
                debugPanel.scrollTop = debugPanel.scrollHeight;
            }
            console.log(`[${timestamp}] ${message}`);
        }

        function startTranslation() {
            log('=== 开始翻译测试 ===');
            log('当前页面段落数量: ' + document.querySelectorAll('p').length);
            log('包含br标签的段落数: ' + Array.from(document.querySelectorAll('p')).filter(p => p.querySelector('br')).length);
            
            // 模拟翻译开始
            setTimeout(() => {
                checkLoadingIcons();
            }, 1000);
        }

        function toggleDebug() {
            debugVisible = !debugVisible;
            debugPanel.style.display = debugVisible ? 'block' : 'none';
            event.target.textContent = debugVisible ? '隐藏调试' : '显示调试';
        }

        function clearDebug() {
            debugPanel.innerHTML = '';
            log('调试日志已清空');
        }

        function checkLoadingIcons() {
            const allIcons = document.querySelectorAll('.translate-icon');
            const splitParagraphs = document.querySelectorAll('.split-paragraph');
            const segmentWrappers = document.querySelectorAll('.segment-wrapper');
            
            log('=== Loading图标检查结果 ===');
            log(`总loading图标数: ${allIcons.length}`);
            log(`拆分段落数: ${splitParagraphs.length}`);
            log(`分段容器数: ${segmentWrappers.length}`);
            
            // 检查每个段落的loading图标数量
            document.querySelectorAll('p').forEach((p, index) => {
                const icons = p.querySelectorAll('.translate-icon');
                const hasBr = p.querySelector('br') !== null;
                const isSplit = p.classList.contains('split-paragraph');
                const segments = p.querySelectorAll('.segment-wrapper');
                
                log(`段落${index + 1}: ${icons.length}个图标, 包含br:${hasBr}, 已拆分:${isSplit}, 分段数:${segments.length}`);
                
                if (hasBr && isSplit) {
                    // 拆分段落应该有多个图标（每个分段一个）
                    if (icons.length !== segments.length) {
                        log(`⚠️ 警告: 拆分段落图标数(${icons.length})与分段数(${segments.length})不匹配`);
                    }
                } else if (!hasBr) {
                    // 简单段落应该只有一个图标
                    if (icons.length !== 1) {
                        log(`⚠️ 警告: 简单段落应该只有1个图标，实际有${icons.length}个`);
                    }
                }
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化测试环境');
            log('总段落数: ' + document.querySelectorAll('p').length);
            log('包含<br>的段落数: ' + Array.from(document.querySelectorAll('p')).filter(p => p.querySelector('br')).length);
        });

        // 重写console.log以便在调试面板中显示
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            // 只记录特定的日志消息到调试面板
            const message = args.join(' ');
            if (message.includes('段落') || message.includes('分段') || message.includes('翻译') || message.includes('跳过')) {
                log(message);
            }
        };
    </script>
</body>
</html>
