<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试简化DOM结构</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        p {
            margin: 15px 0;
            padding: 15px;
            background-color: #fff;
            border-left: 4px solid #007bff;
            border-radius: 4px;
            position: relative;
        }
        strong {
            color: #dc3545;
            font-weight: bold;
        }
        em {
            color: #28a745;
            font-style: italic;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        /* 翻译相关样式 */
        .split-paragraph {
            border-left-color: #ffc107 !important;
            background-color: #fff3cd !important;
        }
        
        /* 分段内容样式 - 现在是简单的span */
        span[data-segment-index] {
            display: inline;
            border: 1px dashed #007bff;
            padding: 2px 4px;
            margin: 0 2px;
            background-color: rgba(0, 123, 255, 0.1);
            border-radius: 3px;
            position: relative;
        }
        span[data-segment-index]::before {
            content: "分段" attr(data-segment-index);
            position: absolute;
            top: -15px;
            left: 0;
            background: #007bff;
            color: white;
            padding: 1px 4px;
            font-size: 9px;
            border-radius: 2px;
            white-space: nowrap;
        }
        
        .translate-icon {
            display: inline-block !important;
            width: 18px !important;
            height: 18px !important;
            background-color: #007bff !important;
            border-radius: 50% !important;
            margin: 0 4px !important;
            position: relative !important;
            animation: pulse 1.5s infinite !important;
            vertical-align: middle !important;
        }
        .translate-icon::after {
            content: "L";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
            font-weight: bold;
        }
        
        .translation-result {
            display: block !important;
            background-color: #d4edda !important;
            border: 1px solid #c3e6cb !important;
            padding: 8px !important;
            margin: 5px 0 !important;
            border-radius: 4px !important;
            color: #155724 !important;
            position: relative !important;
            font-size: 14px !important;
        }
        .translation-result::before {
            content: "翻译结果";
            position: absolute;
            top: -8px;
            left: 8px;
            background: #28a745;
            color: white;
            padding: 1px 6px;
            font-size: 10px;
            border-radius: 3px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        /* 调试面板样式 */
        .debug-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 450px;
            max-height: 700px;
            background: rgba(0,0,0,0.95);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 11px;
            font-family: monospace;
            z-index: 10000;
            overflow-y: auto;
            display: none;
        }
        
        .control-buttons {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 10001;
        }
        
        .control-buttons button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-info { background-color: #17a2b8; color: white; }
        
        .structure-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .expected-structure {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        
        .expected-structure h4 {
            color: #0c5460;
            margin-top: 0;
        }
        
        .expected-structure code {
            background-color: #b8daff;
            color: #004085;
        }
    </style>
</head>
<body>
    <h1>简化DOM结构测试</h1>
    
    <div class="control-buttons">
        <button class="btn-primary" onclick="startTranslation()">开始翻译</button>
        <button class="btn-success" onclick="toggleDebug()">显示调试</button>
        <button class="btn-danger" onclick="clearDebug()">清空日志</button>
        <button class="btn-warning" onclick="analyzeStructure()">分析结构</button>
        <button class="btn-info" onclick="showExpectedStructure()">期望结构</button>
    </div>
    
    <div id="debug-panel" class="debug-panel"></div>
    
    <div class="expected-structure" id="expected-structure" style="display: none;">
        <h4>期望的DOM结构</h4>
        <code>
&lt;p class="split-paragraph"&gt;
  &lt;span data-segment-index="0"&gt;第一行内容&lt;strong&gt;加粗文字&lt;/strong&gt;&lt;/span&gt;
  &lt;span class="translate-icon"&gt;&lt;/span&gt;
  &lt;div class="translation-result"&gt;第一行翻译结果&lt;/div&gt;
  &lt;br&gt;
  &lt;span data-segment-index="1"&gt;第二行内容&lt;em&gt;斜体文字&lt;/em&gt;&lt;/span&gt;
  &lt;span class="translate-icon"&gt;&lt;/span&gt;
  &lt;div class="translation-result"&gt;第二行翻译结果&lt;/div&gt;
  &lt;br&gt;
  &lt;span data-segment-index="2"&gt;第三行内容&lt;/span&gt;
  &lt;span class="translate-icon"&gt;&lt;/span&gt;
  &lt;div class="translation-result"&gt;第三行翻译结果&lt;/div&gt;
&lt;/p&gt;
        </code>
        <p><strong>关键特点：</strong></p>
        <ul>
            <li>不创建额外的包装容器（如 .segment-wrapper）</li>
            <li>直接在原段落结构中插入翻译UI</li>
            <li>保持原有的HTML层级和样式</li>
            <li>翻译结果紧跟在对应分段后面</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">测试1: 包含br标签的段落（应该简化结构）</h2>
        <p>这是第一行文字，包含<strong>加粗文字</strong>，还有普通文字。<br>这是第二行文字，包含<em>斜体文字</em>，以及更多内容。<br>这是第三行文字，包含<code>代码文字</code>和结尾。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试2: 简单段落（应该保持原有逻辑）</h2>
        <p>这是一个简单的段落，不包含换行标签，应该使用原有的翻译逻辑进行处理。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试3: 复杂拆分段落（检查简化后的结构）</h2>
        <p>第一段内容，包含<strong>重要信息</strong>。<br>第二段内容，包含<a href="#">链接文字</a>。<br>第三段内容，包含<span style="color: blue;">彩色文字</span>。<br>第四段内容，这是最后一段。</p>
    </div>

    <div id="structure-display" class="structure-info" style="display: none;">
        <h3>DOM结构分析结果</h3>
        <div id="structure-content"></div>
    </div>

    <script>
        let debugVisible = false;
        let debugPanel = document.getElementById('debug-panel');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${message}`;
            logEntry.style.borderBottom = '1px solid #333';
            logEntry.style.padding = '3px 0';
            logEntry.style.wordBreak = 'break-word';
            debugPanel.appendChild(logEntry);
            if (debugVisible) {
                debugPanel.scrollTop = debugPanel.scrollHeight;
            }
            console.log(`[${timestamp}] ${message}`);
        }

        function startTranslation() {
            log('=== 开始翻译测试 ===');
            log('当前页面段落数量: ' + document.querySelectorAll('p').length);
            log('包含br标签的段落数: ' + Array.from(document.querySelectorAll('p')).filter(p => p.querySelector('br')).length);
        }

        function toggleDebug() {
            debugVisible = !debugVisible;
            debugPanel.style.display = debugVisible ? 'block' : 'none';
            event.target.textContent = debugVisible ? '隐藏调试' : '显示调试';
        }

        function clearDebug() {
            debugPanel.innerHTML = '';
            log('调试日志已清空');
        }

        function showExpectedStructure() {
            const expectedDiv = document.getElementById('expected-structure');
            expectedDiv.style.display = expectedDiv.style.display === 'none' ? 'block' : 'none';
        }

        function analyzeStructure() {
            log('=== DOM结构分析 ===');
            
            const structureDisplay = document.getElementById('structure-display');
            const structureContent = document.getElementById('structure-content');
            
            let analysisResult = '';
            
            document.querySelectorAll('p').forEach((p, index) => {
                const icons = p.querySelectorAll('.translate-icon');
                const results = p.querySelectorAll('.translation-result');
                const segments = p.querySelectorAll('[data-segment-index]');
                const wrappers = p.querySelectorAll('.segment-wrapper'); // 应该为0
                const hasBr = p.querySelector('br') !== null;
                const isSplit = p.classList.contains('split-paragraph');
                
                analysisResult += `段落${index + 1}:\n`;
                analysisResult += `  - 包含br: ${hasBr}\n`;
                analysisResult += `  - 已拆分: ${isSplit}\n`;
                analysisResult += `  - 翻译图标数: ${icons.length}\n`;
                analysisResult += `  - 翻译结果数: ${results.length}\n`;
                analysisResult += `  - 分段数: ${segments.length}\n`;
                analysisResult += `  - 包装容器数: ${wrappers.length} ${wrappers.length > 0 ? '⚠️ 应该为0!' : '✅'}\n`;
                
                if (hasBr && isSplit) {
                    analysisResult += `  DOM结构:\n`;
                    Array.from(p.childNodes).forEach((child, childIndex) => {
                        if (child.nodeType === Node.ELEMENT_NODE) {
                            const element = child as HTMLElement;
                            analysisResult += `    ${childIndex}: <${element.tagName.toLowerCase()}`;
                            if (element.className) analysisResult += ` class="${element.className}"`;
                            if (element.getAttribute('data-segment-index')) {
                                analysisResult += ` data-segment-index="${element.getAttribute('data-segment-index')}"`;
                            }
                            analysisResult += `>\n`;
                        } else if (child.nodeType === Node.TEXT_NODE) {
                            const text = child.textContent?.trim();
                            if (text) analysisResult += `    ${childIndex}: TEXT: "${text}"\n`;
                        }
                    });
                }
                
                analysisResult += '\n';
                
                log(`段落${index + 1}: 图标${icons.length}个, 结果${results.length}个, 分段${segments.length}个, 包装器${wrappers.length}个`);
            });
            
            structureContent.textContent = analysisResult;
            structureDisplay.style.display = 'block';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化测试环境');
            log('总段落数: ' + document.querySelectorAll('p').length);
            log('包含<br>的段落数: ' + Array.from(document.querySelectorAll('p')).filter(p => p.querySelector('br')).length);
        });

        // 重写console.log以便在调试面板中显示
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.join(' ');
            if (message.includes('段落') || message.includes('分段') || message.includes('翻译') || message.includes('处理')) {
                log(message);
            }
        };
    </script>
</body>
</html>
