import { EFloatButtonActionType } from '@src/common/const'
import { ScreenshotIcon, SummaryIcon, TranslateIcon } from './Icon'

export const blankNodeList = [
  'SCRIPT',
  'STYLE',
  'LINK',
  'SVG',
  'KBD',
  'PRE',
  'IMG',
  'PATH',
  'VIDEO',
  'AUDIO',
  'SOURCE',
  'CANVAS',
  'IFRAME',
  'CODE',
  'FOOTER',
  'NAV',
] // 不需要翻译的标签

export const ELE_MIN_WIDTH = 4 // 最小宽度

export const translateClassname = {
  translateIcon: 'translate-icon',
  translationResult: 'translation-result',
  splitParagraph: 'split-paragraph', // 标记已拆分的段落
  segmentWrapper: 'segment-wrapper', // 分段包装容器标记
}

/**
 * 浮动球菜单列表
 */
interface IFloatButtonMenuItem {
  title: string
  Icon: React.FC<React.SVGProps<SVGSVGElement>>
  action: EFloatButtonActionType
}

export const menuItems: IFloatButtonMenuItem[] = [
  {
    title: '总结此页面',
    Icon: SummaryIcon,
    action: EFloatButtonActionType.Summary,
  },
  {
    title: '截图',
    Icon: ScreenshotIcon,
    action: EFloatButtonActionType.Screenshot,
  },
  {
    title: '翻译',
    Icon: TranslateIcon,
    action: EFloatButtonActionType.Translate,
  },
]
