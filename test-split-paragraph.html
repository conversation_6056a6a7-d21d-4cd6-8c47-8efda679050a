<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试段落拆分翻译</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        p {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        strong {
            color: #dc3545;
        }
        em {
            color: #28a745;
        }
    </style>
</head>
<body>
    <h1>段落拆分翻译测试页面</h1>
    
    <div class="test-section">
        <h2 class="test-title">测试1: 包含br标签的复杂段落</h2>
        <p>这是第一行文字，包含<strong>加粗文字</strong>，还有普通文字。<br>这是第二行文字，包含<em>斜体文字</em>，以及更多内容。<br>这是第三行文字，包含<code>代码文字</code>和结尾。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试2: 简单段落（不包含br）</h2>
        <p>这是一个简单的段落，不包含换行标签，应该使用原有的翻译逻辑进行处理。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试3: 多个br标签的复杂段落</h2>
        <p>第一段内容，包含<strong>重要信息</strong>。<br>第二段内容，包含<a href="#">链接文字</a>。<br>第三段内容，包含<span style="color: blue;">彩色文字</span>。<br>第四段内容，这是最后一段。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试4: 嵌套HTML元素的段落</h2>
        <p>这里有<strong>加粗的<em>斜体</em>文字</strong>，还有普通文字。<br>这里有<code>代码块<strong>加粗代码</strong></code>内容。<br>最后是<mark>高亮文字</mark>结尾。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试5: 只有一个br的段落</h2>
        <p>第一行内容，包含一些文字。<br>第二行内容，这是最后一行。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试6: 空br标签的处理</h2>
        <p>有内容的第一行。<br><br>中间有空行。<br>最后一行内容。</p>
    </div>

    <script>
        // 模拟Chrome扩展环境
        if (!window.chrome) {
            window.chrome = {
                runtime: {
                    onMessage: {
                        addListener: function(callback) {
                            console.log('模拟Chrome扩展消息监听器已添加');
                            // 可以在这里模拟消息发送来测试
                        }
                    }
                }
            };
        }

        // 添加测试按钮
        const testButton = document.createElement('button');
        testButton.textContent = '开始翻译测试';
        testButton.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1000;
        `;
        
        testButton.onclick = function() {
            console.log('开始翻译测试...');
            // 这里可以触发翻译逻辑
            if (window.chrome && window.chrome.runtime) {
                // 模拟发送翻译消息
                console.log('发送翻译消息...');
            }
        };
        
        document.body.appendChild(testButton);
    </script>
</body>
</html>
